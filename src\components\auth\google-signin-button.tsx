"use client";

import { signIn } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { SiGoogle } from "react-icons/si";
import { useTranslations } from "next-intl";

interface GoogleSignInButtonProps {
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  callbackUrl?: string;
}

export default function GoogleSignInButton({ 
  className = "",
  variant = "outline",
  size = "default",
  callbackUrl = "/"
}: GoogleSignInButtonProps) {
  const t = useTranslations();

  const handleGoogleSignIn = () => {
    // 直接重定向到Google OAuth，不使用模态框
    signIn("google", { 
      callbackUrl,
      redirect: true // 确保重定向到Google
    });
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`flex items-center justify-center gap-3 ${className}`}
      onClick={handleGoogleSignIn}
    >
      <SiGoogle className="w-4 h-4" />
      <span>{t("sign_modal.google_sign_in")}</span>
    </Button>
  );
}
