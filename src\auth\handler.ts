import { AdapterUser } from "next-auth/adapters";
import { Account, User } from "next-auth";
import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { saveUser } from "@/services/user";
import { User as UserType } from "@/types/user";
import { getClientIp } from "@/lib/ip";

export async function handleSignInUser(
  user: User | AdapterUser,
  account: Account
): Promise<UserType | null> {
  try {
    if (!user.email) {
      throw new Error("invalid signin user");
    }
    if (!account.type || !account.provider || !account.providerAccountId) {
      throw new Error("invalid signin account");
    }

    const userInfo: UserType = {
      uuid: getUuid(),
      email: user.email,
      nickname: user.name || "",
      avatar_url: user.image || "",
      signin_type: account.type,
      signin_provider: account.provider,
      signin_openid: account.providerAccountId,
      created_at: new Date(),
      signin_ip: await getClientIp(),
    };

    const savedUser = await saveUser(userInfo);

    return savedUser;
  } catch (e) {
    console.error("handle signin user failed:", e);

    // 如果是数据库连接问题，返回基本用户信息以允许登录
    if (e instanceof Error && e.message.includes('CONNECT_TIMEOUT')) {
      console.log("Database connection failed, allowing login with basic user info");
      return {
        uuid: user.id || `temp_${Date.now()}`,
        email: user.email || "",
        nickname: user.name || "",
        avatar_url: user.image || "",
        created_at: new Date(),
        id: 0, // 临时ID
        locale: "zh",
        signin_type: account.type,
        signin_ip: "",
        signin_provider: account.provider,
        signin_openid: account.providerAccountId,
        invite_code: null,
        updated_at: new Date(),
        invited_by: null,
        is_affiliate: false,
        is_admin: false
      };
    }

    throw e;
  }
}
