"use client";

import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import GoogleSignInButton from "./google-signin-button";

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export default function AuthGuard({ 
  children, 
  fallback,
  redirectTo,
  requireAuth = true 
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return; // 还在加载中

    if (requireAuth && !session && redirectTo) {
      router.push(redirectTo);
    }
  }, [session, status, router, redirectTo, requireAuth]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (requireAuth && !session) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex flex-col items-center justify-center min-h-[300px] p-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">需要登录</h3>
          <p className="text-gray-600">请使用Google账户登录以继续使用服务</p>
        </div>
        <GoogleSignInButton 
          className="w-full max-w-sm"
          size="lg"
        />
      </div>
    );
  }

  return <>{children}</>;
}
