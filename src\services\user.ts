import { CreditsAmount, CreditsTransType } from "./credit";
import { findUserByEmail, findUserByUuid, insertUser, updateUserInfo } from "@/models/user";

import { User } from "@/types/user";
import { auth } from "@/auth";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";
import { getUserUuidByApiKey } from "@/models/apikey";
import { headers } from "next/headers";
import { increaseCredits } from "./credit";
import { users } from "@/db/schema";
import { getUuid } from "@/lib/hash";

// null转undefined辅助函数
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

// save user to database, if user not exist, create a new user
export async function saveUser(user: User) {
  try {
    if (!user.email) {
      throw new Error("invalid user email");
    }

    const existUser = await findUserByEmail(user.email);

    if (!existUser) {
      // user not exist, create a new user
      if (!user.uuid) {
        user.uuid = getUuid();
      }

      console.log("user to be inserted:", user);

      const dbUser = await insertUser(user as typeof users.$inferInsert);

      // increase credits for new user, expire in one year
      await increaseCredits({
        user_uuid: user.uuid,
        trans_type: CreditsTransType.NewUser,
        credits: CreditsAmount.NewUserGet,
        expired_at: getOneYearLaterTimestr(),
      });

      user = {
        ...(dbUser as unknown as User),
      };
    } else {
      // user exist, update user info (nickname and avatar_url) and return
      // 确保头像URL得到更新，特别是Google头像
      const updatedUser = await updateUserInfo(existUser.uuid, {
        nickname: user.nickname === null ? undefined : user.nickname,
        avatar_url: user.avatar_url === null ? undefined : user.avatar_url,
      });

      user = {
        ...(updatedUser as unknown as User),
      };
    }

    return user;
  } catch (e) {
    console.log("save user failed: ", e);

    // 如果是数据库连接问题，返回基本用户信息
    if (e instanceof Error && e.message.includes('CONNECT_TIMEOUT')) {
      console.log("Database connection failed in saveUser, returning basic user info");
      return {
        ...user,
        id: 0, // 临时ID
        uuid: user.uuid || `temp_${Date.now()}`,
        created_at: user.created_at || new Date(),
        updated_at: new Date(),
        locale: user.locale || "zh",
        signin_ip: user.signin_ip || "",
        invite_code: user.invite_code || null,
        invited_by: user.invited_by || null,
        is_affiliate: user.is_affiliate || false,
        is_admin: user.is_admin || false
      };
    }

    throw e;
  }
}

export async function getUserUuid() {
  let user_uuid = "";

  const token = await getBearerToken();

  if (token) {
    // api key
    if (token.startsWith("sk-")) {
      const user_uuid = await getUserUuidByApiKey(token);

      return user_uuid || "";
    }
  }

  const session = await auth();
  if (session && session.user && session.user.uuid) {
    user_uuid = session.user.uuid;
  }

  return user_uuid;
}

export async function getBearerToken() {
  const h = await headers();
  const auth = h.get("Authorization");
  if (!auth) {
    return "";
  }

  return auth.replace("Bearer ", "");
}

export async function getUserEmail() {
  let user_email = "";

  const session = await auth();
  if (session && session.user && session.user.email) {
    user_email = session.user.email;
  }

  return user_email;
}

export async function getUserInfo() {
  let user_uuid = await getUserUuid();

  if (!user_uuid) {
    return;
  }

  const user = await findUserByUuid(user_uuid);

  return user;
}
