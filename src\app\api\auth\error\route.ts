import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const error = searchParams.get('error');
  
  // 根据错误类型返回友好的错误信息
  let errorMessage = '登录失败，请稍后重试';
  let errorCode = 'UNKNOWN_ERROR';
  
  switch (error) {
    case 'Configuration':
      errorMessage = '登录服务配置错误，请联系管理员';
      errorCode = 'CONFIG_ERROR';
      break;
    case 'AccessDenied':
      errorMessage = '登录被拒绝，请检查您的账户权限';
      errorCode = 'ACCESS_DENIED';
      break;
    case 'Verification':
      errorMessage = '邮箱验证失败，请重新尝试';
      errorCode = 'VERIFICATION_FAILED';
      break;
    case 'Default':
    default:
      errorMessage = '登录过程中发生错误，请稍后重试';
      errorCode = 'LOGIN_FAILED';
      break;
  }
  
  // 重定向到主页并带上错误信息
  const redirectUrl = new URL('/', request.url);
  redirectUrl.searchParams.set('login_error', errorCode);
  redirectUrl.searchParams.set('error_message', encodeURIComponent(errorMessage));
  
  return NextResponse.redirect(redirectUrl);
}
