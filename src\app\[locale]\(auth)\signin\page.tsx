"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import GoogleSignInButton from "@/components/auth/google-signin-button";

export default function SignInPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const error = searchParams.get('error');

  useEffect(() => {
    // 如果已经登录，重定向到回调URL
    if (session) {
      router.push(callbackUrl);
    }
  }, [session, router, callbackUrl]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (session) {
    return null; // 重定向中
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            登录到 Watermark Remover
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            使用您的Google账户安全登录
          </p>
        </div>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  登录失败
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>登录过程中发生错误，请重新尝试。</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          <GoogleSignInButton 
            className="w-full justify-center py-3 px-4 border-2 border-blue-300 hover:border-blue-500"
            size="lg"
            callbackUrl={callbackUrl}
          />
          
          <div className="text-center">
            <div className="text-sm text-gray-500 space-y-2">
              <p>通过登录，您同意我们的服务条款和隐私政策</p>
              <p>我们仅使用您的邮箱地址进行账户识别</p>
            </div>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-200 pt-6">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              还没有账户？登录时会自动为您创建
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
