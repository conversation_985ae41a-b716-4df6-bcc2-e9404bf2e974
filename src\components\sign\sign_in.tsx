"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations();

  const handleSignIn = () => {
    // 直接重定向到NextAuth标准登录页面
    window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.href);
  };

  return (
    <Button
      variant="default"
      onClick={handleSignIn}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
